#!/usr/bin/env python3
"""
Enhanced Ensemble System - Phase 2 Development
Advanced ensemble voting with dynamic weights, regime detection, and confidence calibration
"""

import time
import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import sqlite3
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    BULL = "bull"
    BEAR = "bear"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"

class ActionType(Enum):
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"

@dataclass
class ModelPerformance:
    model_name: str
    accuracy: float
    sharpe_ratio: float
    win_rate: float
    avg_return: float
    volatility: float
    last_updated: datetime
    regime_performance: Dict[MarketRegime, float]

@dataclass
class EnsembleDecision:
    action: ActionType
    confidence: float
    consensus_strength: float
    participating_models: int
    regime: MarketRegime
    dynamic_weights: Dict[str, float]
    uncertainty_score: float
    reasoning: str
    timestamp: datetime

class EnhancedEnsembleSystem:
    """Enhanced ensemble system with dynamic weights and regime detection"""
    
    def __init__(self):
        self.db_path = "enhanced_ensemble.db"
        self.initialize_database()
        
        # Base model weights (will be dynamically adjusted)
        self.base_weights = {
            'ultimate-speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest': 0.15,
            'quality_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest': 0.15,
            'balanced-phase2-unrestricted-noryon-phi-4-9b-finance-latest': 0.12,
            'speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest': 0.12,
            'phase2-unrestricted-noryon-qwen3-finance-v2-latest': 0.15,
            'noryon-gemma-3-12b-finance': 0.12,
            'noryon-deepseek-r1-finance': 0.10,
            'noryon-marco-o1-finance': 0.09
        }
        
        # Regime-specific weight adjustments
        self.regime_adjustments = {
            MarketRegime.BULL: {
                'momentum_models': 1.2,
                'risk_models': 0.8
            },
            MarketRegime.BEAR: {
                'momentum_models': 0.8,
                'risk_models': 1.3
            },
            MarketRegime.HIGH_VOLATILITY: {
                'fast_models': 1.3,
                'analytical_models': 0.9
            },
            MarketRegime.SIDEWAYS: {
                'analytical_models': 1.2,
                'momentum_models': 0.8
            }
        }
        
        # Model specializations
        self.model_specializations = {
            'ultimate-speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest': 'fast_models',
            'quality_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest': 'analytical_models',
            'balanced-phase2-unrestricted-noryon-phi-4-9b-finance-latest': 'balanced_models',
            'speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest': 'fast_models',
            'phase2-unrestricted-noryon-qwen3-finance-v2-latest': 'analytical_models',
            'noryon-gemma-3-12b-finance': 'momentum_models',
            'noryon-deepseek-r1-finance': 'risk_models',
            'noryon-marco-o1-finance': 'analytical_models'
        }
    
    def initialize_database(self):
        """Initialize enhanced ensemble database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Model performance tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_name TEXT NOT NULL,
                accuracy REAL,
                sharpe_ratio REAL,
                win_rate REAL,
                avg_return REAL,
                volatility REAL,
                regime TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Ensemble decisions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ensemble_decisions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT,
                action TEXT,
                confidence REAL,
                consensus_strength REAL,
                participating_models INTEGER,
                regime TEXT,
                uncertainty_score REAL,
                reasoning TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Market regime history
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS market_regimes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                regime TEXT,
                confidence REAL,
                indicators TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def detect_market_regime(self, market_data: Dict[str, Any]) -> Tuple[MarketRegime, float]:
        """Detect current market regime using technical indicators"""
        
        # Simplified regime detection (in production, use more sophisticated indicators)
        volatility = market_data.get('volatility', 0.15)
        trend_strength = market_data.get('trend_strength', 0.5)
        momentum = market_data.get('momentum', 0.0)
        
        regime_scores = {
            MarketRegime.HIGH_VOLATILITY: min(volatility * 5, 1.0),
            MarketRegime.LOW_VOLATILITY: max(1.0 - volatility * 5, 0.0),
            MarketRegime.BULL: max(momentum * 2, 0.0) if momentum > 0 else 0.0,
            MarketRegime.BEAR: max(-momentum * 2, 0.0) if momentum < 0 else 0.0,
            MarketRegime.SIDEWAYS: max(1.0 - abs(momentum) * 3, 0.0)
        }
        
        # Select regime with highest score
        best_regime = max(regime_scores, key=regime_scores.get)
        confidence = regime_scores[best_regime]
        
        # Store regime detection
        self._store_regime_detection(best_regime, confidence, market_data)
        
        return best_regime, confidence
    
    def calculate_dynamic_weights(self, regime: MarketRegime, 
                                performance_data: Dict[str, ModelPerformance]) -> Dict[str, float]:
        """Calculate dynamic weights based on regime and recent performance"""
        
        dynamic_weights = self.base_weights.copy()
        
        # Adjust weights based on regime
        for model_name, base_weight in dynamic_weights.items():
            specialization = self.model_specializations.get(model_name, 'balanced_models')
            
            # Apply regime-specific adjustments
            if regime in self.regime_adjustments:
                adjustment = self.regime_adjustments[regime].get(specialization, 1.0)
                dynamic_weights[model_name] *= adjustment
            
            # Apply performance-based adjustments
            if model_name in performance_data:
                perf = performance_data[model_name]
                
                # Boost weights for high-performing models
                performance_multiplier = 1.0 + (perf.accuracy - 0.5) * 0.5
                performance_multiplier = max(0.5, min(1.5, performance_multiplier))
                
                dynamic_weights[model_name] *= performance_multiplier
        
        # Normalize weights to sum to 1.0
        total_weight = sum(dynamic_weights.values())
        if total_weight > 0:
            dynamic_weights = {k: v/total_weight for k, v in dynamic_weights.items()}
        
        return dynamic_weights
    
    def calibrate_confidence(self, raw_confidence: float, 
                           consensus_strength: float,
                           regime_confidence: float) -> float:
        """Calibrate confidence based on consensus and regime certainty"""
        
        # Base confidence adjustment
        calibrated = raw_confidence
        
        # Adjust based on consensus strength
        consensus_factor = 0.5 + (consensus_strength * 0.5)
        calibrated *= consensus_factor
        
        # Adjust based on regime confidence
        regime_factor = 0.7 + (regime_confidence * 0.3)
        calibrated *= regime_factor
        
        # Ensure confidence stays within bounds
        return max(0.1, min(0.95, calibrated))
    
    def calculate_uncertainty_score(self, model_votes: List[Dict], 
                                  consensus_strength: float) -> float:
        """Calculate uncertainty score based on model disagreement"""
        
        if len(model_votes) < 2:
            return 0.5  # High uncertainty with few models
        
        # Calculate action distribution
        actions = [vote['action'] for vote in model_votes]
        action_counts = {action.value: actions.count(action) for action in ActionType}
        
        # Calculate entropy (disagreement measure)
        total_votes = len(model_votes)
        entropy = 0.0
        for count in action_counts.values():
            if count > 0:
                prob = count / total_votes
                entropy -= prob * np.log2(prob)
        
        # Normalize entropy (max entropy for 3 actions is log2(3))
        max_entropy = np.log2(len(ActionType))
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0
        
        # Combine with consensus strength
        uncertainty = (normalized_entropy + (1.0 - consensus_strength)) / 2.0
        
        return min(1.0, max(0.0, uncertainty))
    
    def make_enhanced_decision(self, symbol: str, model_votes: List[Dict], 
                             market_data: Dict[str, Any]) -> EnsembleDecision:
        """Make enhanced ensemble decision with all improvements"""
        
        start_time = time.time()
        
        # Detect market regime
        regime, regime_confidence = self.detect_market_regime(market_data)
        
        # Get recent performance data
        performance_data = self._get_recent_performance()
        
        # Calculate dynamic weights
        dynamic_weights = self.calculate_dynamic_weights(regime, performance_data)
        
        # Calculate weighted votes
        weighted_scores = {action.value: 0.0 for action in ActionType}
        total_weight = 0.0
        participating_models = 0
        
        for vote in model_votes:
            model_name = vote.get('model_name', 'unknown')
            action = vote.get('action', ActionType.HOLD)
            confidence = vote.get('confidence', 0.5)
            
            if model_name in dynamic_weights:
                weight = dynamic_weights[model_name] * confidence
                weighted_scores[action.value] += weight
                total_weight += weight
                participating_models += 1
        
        # Normalize scores
        if total_weight > 0:
            weighted_scores = {k: v/total_weight for k, v in weighted_scores.items()}
        
        # Determine final action
        final_action = ActionType(max(weighted_scores, key=weighted_scores.get))
        raw_confidence = weighted_scores[final_action.value]
        
        # Calculate consensus strength
        consensus_strength = self._calculate_consensus_strength(model_votes)
        
        # Calculate uncertainty score
        uncertainty_score = self.calculate_uncertainty_score(model_votes, consensus_strength)
        
        # Calibrate final confidence
        calibrated_confidence = self.calibrate_confidence(
            raw_confidence, consensus_strength, regime_confidence
        )
        
        # Generate reasoning
        reasoning = self._generate_reasoning(
            final_action, regime, participating_models, 
            consensus_strength, uncertainty_score
        )
        
        # Create enhanced decision
        decision = EnsembleDecision(
            action=final_action,
            confidence=calibrated_confidence,
            consensus_strength=consensus_strength,
            participating_models=participating_models,
            regime=regime,
            dynamic_weights=dynamic_weights,
            uncertainty_score=uncertainty_score,
            reasoning=reasoning,
            timestamp=datetime.now()
        )
        
        # Store decision
        self._store_decision(symbol, decision)
        
        analysis_time = time.time() - start_time
        logger.info(f"Enhanced ensemble decision completed in {analysis_time:.2f}s")
        
        return decision
    
    def _calculate_consensus_strength(self, model_votes: List[Dict]) -> float:
        """Calculate consensus strength among models"""
        if len(model_votes) < 2:
            return 0.5
        
        actions = [vote['action'] for vote in model_votes]
        most_common_action = max(set(actions), key=actions.count)
        agreement_count = actions.count(most_common_action)
        
        return agreement_count / len(model_votes)
    
    def _generate_reasoning(self, action: ActionType, regime: MarketRegime,
                          participating_models: int, consensus_strength: float,
                          uncertainty_score: float) -> str:
        """Generate human-readable reasoning for the decision"""
        
        reasoning_parts = [
            f"Enhanced ensemble decision: {action.value}",
            f"Market regime: {regime.value}",
            f"Models participating: {participating_models}",
            f"Consensus strength: {consensus_strength:.1%}",
            f"Uncertainty: {uncertainty_score:.1%}"
        ]
        
        if consensus_strength > 0.8:
            reasoning_parts.append("Strong model agreement supports this decision")
        elif consensus_strength < 0.6:
            reasoning_parts.append("Models show some disagreement - proceed with caution")
        
        if uncertainty_score > 0.7:
            reasoning_parts.append("High uncertainty detected - consider reducing position size")
        
        return " | ".join(reasoning_parts)
    
    def _get_recent_performance(self) -> Dict[str, ModelPerformance]:
        """Get recent model performance data"""
        # Simplified - in production, calculate from actual trading results
        performance_data = {}
        
        for model_name in self.base_weights.keys():
            performance_data[model_name] = ModelPerformance(
                model_name=model_name,
                accuracy=0.65 + np.random.normal(0, 0.1),  # Simulated performance
                sharpe_ratio=1.2 + np.random.normal(0, 0.3),
                win_rate=0.58 + np.random.normal(0, 0.05),
                avg_return=0.08 + np.random.normal(0, 0.02),
                volatility=0.15 + np.random.normal(0, 0.03),
                last_updated=datetime.now(),
                regime_performance={}
            )
        
        return performance_data
    
    def _store_regime_detection(self, regime: MarketRegime, confidence: float, 
                              indicators: Dict[str, Any]):
        """Store regime detection in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO market_regimes (regime, confidence, indicators)
            VALUES (?, ?, ?)
        ''', (regime.value, confidence, json.dumps(indicators)))
        
        conn.commit()
        conn.close()
    
    def _store_decision(self, symbol: str, decision: EnsembleDecision):
        """Store ensemble decision in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO ensemble_decisions 
            (symbol, action, confidence, consensus_strength, participating_models, 
             regime, uncertainty_score, reasoning)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            symbol, decision.action.value, decision.confidence,
            decision.consensus_strength, decision.participating_models,
            decision.regime.value, decision.uncertainty_score, decision.reasoning
        ))
        
        conn.commit()
        conn.close()

def test_enhanced_ensemble():
    """Test the enhanced ensemble system"""
    print("🚀 Testing Enhanced Ensemble System")
    print("=" * 50)
    
    ensemble = EnhancedEnsembleSystem()
    
    # Simulate model votes
    model_votes = [
        {'model_name': 'ultimate-speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest', 
         'action': ActionType.BUY, 'confidence': 0.85},
        {'model_name': 'quality_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest', 
         'action': ActionType.BUY, 'confidence': 0.78},
        {'model_name': 'phase2-unrestricted-noryon-qwen3-finance-v2-latest', 
         'action': ActionType.HOLD, 'confidence': 0.65},
        {'model_name': 'noryon-deepseek-r1-finance', 
         'action': ActionType.BUY, 'confidence': 0.72}
    ]
    
    # Simulate market data
    market_data = {
        'volatility': 0.25,
        'trend_strength': 0.7,
        'momentum': 0.3,
        'volume': 1.2
    }
    
    # Make enhanced decision
    decision = ensemble.make_enhanced_decision('AAPL', model_votes, market_data)
    
    print(f"✅ Enhanced Decision: {decision.action.value}")
    print(f"📊 Confidence: {decision.confidence:.1%}")
    print(f"🤝 Consensus: {decision.consensus_strength:.1%}")
    print(f"🌍 Regime: {decision.regime.value}")
    print(f"❓ Uncertainty: {decision.uncertainty_score:.1%}")
    print(f"🧠 Reasoning: {decision.reasoning}")
    print(f"⚖️ Dynamic Weights: {len(decision.dynamic_weights)} models weighted")
    
    print("\n✅ Enhanced Ensemble System Test Complete!")

if __name__ == "__main__":
    test_enhanced_ensemble()
